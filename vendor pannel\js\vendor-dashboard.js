// Vendor Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
  // Check if vendor is authenticated
  if (!vendorAuthService.isAuthenticated() || !vendorAuthService.isVendor()) {
    vendorAuthService.redirectToLogin();
    return;
  }

  // Initialize dashboard functionality
  initializeVendorDashboard();
});

function initializeVendorDashboard() {
  // Note: VendorUtils now handles logout and vendor info display
  // We only need to handle dashboard-specific functionality

  // Load vendor dashboard statistics
  loadVendorStats();

  // Load sidebar navigation counts
  loadSidebarCounts();

  // Setup other dashboard features
  setupSearch();
}

function setupLogout() {
  // Find all logout links
  const logoutLinks = document.querySelectorAll('a[href="logout.html"]');

  logoutLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();

      // Show confirmation dialog
      if (confirm('Are you sure you want to logout? You will be redirected to the main login page.')) {
        // Show logout message
        VendorDashboard.showSuccess('Logging out... Redirecting to main login page.');

        // Logout after short delay to show message
        setTimeout(() => {
          vendorAuthService.logout();
        }, 1000);
      }
    });
  });
}

async function loadVendorInfo() {
  try {
    const user = vendorAuthService.getUser();
    if (user) {
      // Update vendor name in navigation
      const vendorNameElements = document.querySelectorAll('.nav-up__user');
      vendorNameElements.forEach(element => {
        // Keep the icon but update the text
        const iconWrapper = element.querySelector('.nav-up__user-icon-wrapper');
        if (iconWrapper) {
          element.innerHTML = '';
          element.appendChild(iconWrapper);
          element.appendChild(document.createTextNode(user.email.split('@')[0]));
        }
      });
    }

    // Try to load full vendor profile
    try {
      const profileResponse = await vendorAuthService.getProfile();
      if (profileResponse.success) {
        const profile = profileResponse.data.vendor.profile;
        
        // Update vendor name with business name if available
        const vendorNameElements = document.querySelectorAll('.nav-up__user');
        vendorNameElements.forEach(element => {
          const iconWrapper = element.querySelector('.nav-up__user-icon-wrapper');
          if (iconWrapper && profile.business_name) {
            element.innerHTML = '';
            element.appendChild(iconWrapper);
            element.appendChild(document.createTextNode(profile.business_name));
          }
        });
        
        // Update page title if on dashboard
        const pageTitle = document.querySelector('.nav-up__current-page');
        if (pageTitle && profile.business_name) {
          pageTitle.textContent = `${profile.business_name} - Dashboard`;
        }
      }
    } catch (profileError) {
      console.log('Could not load vendor profile:', profileError.message);
    }
    
  } catch (error) {
    console.error('Error loading vendor info:', error);
  }
}

async function loadVendorStats() {
  try {
    // Get the token from localStorage (using the same key as vendor auth service)
    const token = localStorage.getItem('skeyy_auth_token');

    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch('http://localhost:5000/api/vendor/stats', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (data.success && data.data && data.data.stats) {
      const stats = data.data.stats;
      console.log('Vendor stats loaded:', stats);

      // Update dashboard cards with real data
      updateDashboardStats(stats);

      // Update recent orders and products
      updateRecentData(stats);

    } else {
      throw new Error(data.message || 'Failed to load vendor statistics');
    }

  } catch (error) {
    console.error('Error loading vendor stats:', error);

    // Show error message (using alert for now since VendorDashboard might not be defined)
    console.error('Failed to load dashboard statistics: ' + error.message);

    // Show default/error state
    showDefaultStats();
  }
}

function updateDashboardStats(stats) {
  // Update sales this month
  const monthSalesElement = document.getElementById('month-sales-amount');
  const monthGrowthElement = document.getElementById('month-growth-text');
  const monthGrowthIndicator = document.getElementById('month-growth-indicator');

  if (monthSalesElement && stats.sales) {
    monthSalesElement.textContent = `₹${stats.sales.thisMonth.amount.toLocaleString()}`;

    if (monthGrowthElement && monthGrowthIndicator) {
      const growth = stats.sales.thisMonth.growthPercentage;
      if (growth > 0) {
        monthGrowthElement.textContent = `↑ ${growth.toFixed(1)}%`;
        monthGrowthIndicator.className = 'dashboard__status dashboard__status--positive';
      } else if (growth < 0) {
        monthGrowthElement.textContent = `↓ ${Math.abs(growth).toFixed(1)}%`;
        monthGrowthIndicator.className = 'dashboard__status dashboard__status--negative';
      } else {
        monthGrowthElement.textContent = '→ 0%';
        monthGrowthIndicator.className = 'dashboard__status';
      }
    }
  }

  // Update sales today
  const todaySalesElement = document.getElementById('today-sales-amount');
  const todayOrdersElement = document.getElementById('today-orders-count');

  if (todaySalesElement && stats.sales) {
    todaySalesElement.textContent = `₹${stats.sales.today.amount.toLocaleString()}`;
  }

  if (todayOrdersElement && stats.sales) {
    const orderCount = stats.sales.today.orders;
    todayOrdersElement.textContent = `${orderCount} order${orderCount !== 1 ? 's' : ''}`;
  }

  // Update product statistics
  const totalProductsElement = document.getElementById('total-products-count');
  const totalStockElement = document.getElementById('total-stock-count');
  const lowStockElement = document.getElementById('low-stock-count');

  if (totalProductsElement) {
    totalProductsElement.textContent = `${stats.totalProducts} product${stats.totalProducts !== 1 ? 's' : ''}`;
  }

  if (totalStockElement) {
    totalStockElement.textContent = stats.totalStock.toLocaleString();
  }

  if (lowStockElement) {
    const lowStockCount = stats.lowStockProducts;
    lowStockElement.textContent = `${lowStockCount} item${lowStockCount !== 1 ? 's' : ''}`;
  }
}

function updateRecentData(stats) {
  // Update recent orders
  const recentOrdersList = document.getElementById('recent-orders-list');
  if (recentOrdersList && stats.recentOrders) {
    if (stats.recentOrders.length === 0) {
      recentOrdersList.innerHTML = '<div class="loading-message">No recent orders found</div>';
    } else {
      recentOrdersList.innerHTML = stats.recentOrders.map(order => `
        <div class="order-item">
          <div class="order-info">
            <h4>Order #${order.order_number}</h4>
            <p>${new Date(order.created_at).toLocaleDateString()}</p>
          </div>
          <div class="order-amount">₹${order.total_amount.toLocaleString()}</div>
          <div class="order-status order-status--${order.order_status}">${order.order_status}</div>
        </div>
      `).join('');
    }
  }

  // Update top products
  const topProductsList = document.getElementById('top-products-list');
  if (topProductsList && stats.recentProducts) {
    console.log('Updating top products with', stats.recentProducts.length, 'products');
    if (stats.recentProducts.length === 0) {
      topProductsList.innerHTML = '<div class="loading-message">No products found</div>';
    } else {
      const cardsHTML = stats.recentProducts.map(product =>
        createVendorProductCard(product)
      ).join('');
      console.log('Generated cards HTML:', cardsHTML);
      topProductsList.innerHTML = cardsHTML;
    }
  }
}

function createVendorProductCard(product) {
  console.log('Creating card for product:', product.name);
  const originalPrice = product.price;
  const discountedPrice = product.discount > 0 ? originalPrice - (originalPrice * product.discount / 100) : originalPrice;
  const imageUrl = getProductImageUrl(product);
  const hasDiscount = product.discount && product.discount > 0;

  // Get category info if available
  const categoryInfo = product.subcategory?.category?.name && product.subcategory?.name
    ? `${product.subcategory.category.name} > ${product.subcategory.name}`
    : 'Uncategorized';

  const cardHTML = `
    <div class="product-card" data-product-id="${product.id}" data-dynamic="true">
      <div class="product-card__img-wrapper">
        ${hasDiscount ? `<div class="product-card__discount">${product.discount}% OFF</div>` : ''}
        <img src="${imageUrl}" alt="${product.name}" class="product-card__img" onerror="this.src='img/placeholder.jpg'" />
      </div>
      <div class="product-card__content">
        <div class="product-card__category">${categoryInfo}</div>
        <div class="bold s">${product.name}</div>
        <div class="product-card__price">
          ${hasDiscount ? `<span class="product-card__original-price">₹${originalPrice.toLocaleString()}</span>` : ''}
          <span class="product-card__final-price">₹${discountedPrice.toLocaleString()}</span>
        </div>
        <div class="product-card__stock">Stock: ${product.stock} units</div>
      </div>
    </div>
  `;

  console.log('Generated card HTML:', cardHTML);
  return cardHTML;
}

function getProductImageUrl(product) {
  if (product.image_1_url) {
    if (product.image_1_url.startsWith('http')) {
      return product.image_1_url;
    }
    if (product.image_1_url.startsWith('/')) {
      return `http://localhost:5000${product.image_1_url}`;
    }
    return `http://localhost:5000/${product.image_1_url}`;
  }

  if (product.img_url) {
    if (product.img_url.startsWith('http')) {
      return product.img_url;
    }
    if (product.img_url.startsWith('/')) {
      return `http://localhost:5000${product.img_url}`;
    }
    return `http://localhost:5000/${product.img_url}`;
  }

  return 'img/placeholder.jpg';
}

function showDefaultStats() {
  // Show default values when stats fail to load
  const elements = {
    'month-sales-amount': '₹0',
    'month-growth-text': 'No data',
    'today-sales-amount': '₹0',
    'today-orders-count': '0 orders',
    'total-products-count': '0 products',
    'total-stock-count': '0',
    'low-stock-count': '0 items'
  };

  Object.entries(elements).forEach(([id, text]) => {
    const element = document.getElementById(id);
    if (element) {
      element.textContent = text;
    }
  });

  // Show error messages in data sections
  const recentOrdersList = document.getElementById('recent-orders-list');
  const topProductsList = document.getElementById('top-products-list');

  if (recentOrdersList) {
    recentOrdersList.innerHTML = '<div class="loading-message">Failed to load recent orders</div>';
  }

  if (topProductsList) {
    topProductsList.innerHTML = '<div class="loading-message">Failed to load products</div>';
  }
}

function setupSearch() {
  const searchForm = document.querySelector('#js-search-form');
  const searchInput = document.querySelector('#js-search');

  if (searchForm && searchInput) {
    searchForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const searchTerm = searchInput.value.trim();
      if (searchTerm) {
        // Implement search functionality here
        console.log('Searching for:', searchTerm);
        // For now, just show an alert
        alert(`Search functionality coming soon! You searched for: "${searchTerm}"`);
      }
    });
  }
}

async function loadSidebarCounts() {
  try {
    const token = localStorage.getItem('skeyy_auth_token');
    if (!token) return;

    // Load order statistics
    const orderResponse = await fetch('http://localhost:5000/api/vendor/orders/stats', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (orderResponse.ok) {
      const orderData = await orderResponse.json();
      if (orderData.success) {
        updateOrderCounts(orderData.data);
      }
    }

    // Load return statistics
    const returnResponse = await fetch('http://localhost:5000/api/vendor/returns/stats', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (returnResponse.ok) {
      const returnData = await returnResponse.json();
      if (returnData.success) {
        updateReturnCounts(returnData.data);
      }
    }

  } catch (error) {
    console.error('Error loading sidebar counts:', error);
  }
}

function updateOrderCounts(stats) {
  // Update order navigation counts
  const unfulfilledOrderLink = document.querySelector('a[href="orders-unfulfilled.html"]');
  const fulfilledOrderLink = document.querySelector('a[href="orders-fulfilled.html"]');

  if (unfulfilledOrderLink) {
    unfulfilledOrderLink.textContent = `Unfulfilled (${stats.unfulfilled_orders || 0})`;
  }

  if (fulfilledOrderLink) {
    fulfilledOrderLink.textContent = `Fulfilled (${stats.fulfilled_orders || 0})`;
  }
}

function updateReturnCounts(stats) {
  // Update return navigation counts
  const unfulfilledReturnLink = document.querySelector('a[href="returns-unfulfilled.html"]');
  const fulfilledReturnLink = document.querySelector('a[href="returns-fulfilled.html"]');

  if (unfulfilledReturnLink) {
    unfulfilledReturnLink.textContent = `Unfulfilled (${stats.unfulfilled_returns || 0})`;
  }

  if (fulfilledReturnLink) {
    fulfilledReturnLink.textContent = `Fulfilled (${stats.fulfilled_returns || 0})`;
  }
}

// Utility functions for vendor dashboard
const VendorDashboard = {
  // Show success message
  showSuccess: function(message) {
    this.showMessage(message, 'success');
  },
  
  // Show error message
  showError: function(message) {
    this.showMessage(message, 'error');
  },
  
  // Show message with type
  showMessage: function(message, type = 'info') {
    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `dashboard-message dashboard-message--${type}`;
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      z-index: 1000;
      max-width: 300px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    `;
    
    // Set background color based on type
    switch (type) {
      case 'success':
        messageDiv.style.backgroundColor = '#28a745';
        break;
      case 'error':
        messageDiv.style.backgroundColor = '#dc3545';
        break;
      case 'warning':
        messageDiv.style.backgroundColor = '#ffc107';
        messageDiv.style.color = '#212529';
        break;
      default:
        messageDiv.style.backgroundColor = '#17a2b8';
    }
    
    messageDiv.textContent = message;
    
    // Add to page
    document.body.appendChild(messageDiv);
    
    // Remove after 5 seconds
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.parentNode.removeChild(messageDiv);
      }
    }, 5000);
  },
  
  // Format currency
  formatCurrency: function(amount) {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  },
  
  // Format date
  formatDate: function(dateString) {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
};

// Make VendorDashboard available globally
window.VendorDashboard = VendorDashboard;
